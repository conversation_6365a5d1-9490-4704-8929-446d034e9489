import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_color_extensions.dart';
import '../../../core/utils/color_utils.dart';

/// Subscription-specific branding configuration that ensures consistent
/// styling across all subscription management screens
class SubscriptionBrandingConfig {
  // === Color Scheme ===
  // Context-aware color getters that adapt to theme changes

  /// Returns the brand primary color from the current [ColorScheme].
  static Color primary(BuildContext context) => context.colors.primary;

  /// Secondary (brand accent) color derived from the active [ColorScheme].
  static Color secondary(BuildContext context) => context.colors.secondary;

  /// Tertiary color (Turbo Teal).
  static Color tertiary(BuildContext context) => context.colors.tertiary;

  /// Success color that adapts to theme.
  static Color success(BuildContext context) => context.colors.success;

  /// Warning color that adapts to theme.
  static Color warning(BuildContext context) => context.colors.warning;

  /// Error color taken from the theme.
  static Color error(BuildContext context) => context.colors.error;

  /// Info color for trial subscriptions
  static Color info(BuildContext context) => Theme.of(context).colorScheme.surfaceContainerHighest;

  /// Price color with context awareness
  static Color price(BuildContext context) => Theme.of(context).colorScheme.primary;

  // === Legacy compatibility (marked as deprecated) ===
  @Deprecated('Use SubscriptionBrandingConfig.primary(context) instead')
  static Color primaryColor(BuildContext context) => Theme.of(context).colorScheme.primary;
  @Deprecated('Use SubscriptionBrandingConfig.primary(context) instead')
  static Color primaryLightColor(BuildContext context) => Theme.of(context).colorScheme.primary.withOpacity(0.7);
  @Deprecated('Use SubscriptionBrandingConfig.primary(context) instead')
  static Color primaryDarkColor(BuildContext context) => Theme.of(context).colorScheme.primary.withOpacity(0.9);

  @Deprecated('Use SubscriptionBrandingConfig.secondary(context) instead')
  static Color secondaryColor(BuildContext context) => Theme.of(context).colorScheme.secondary;
  @Deprecated('Use SubscriptionBrandingConfig.secondary(context) instead')
  static Color secondaryLightColor(BuildContext context) => Theme.of(context).colorScheme.secondary.withOpacity(0.7);
  @Deprecated('Use SubscriptionBrandingConfig.secondary(context) instead')
  static Color secondaryDarkColor(BuildContext context) => Theme.of(context).colorScheme.secondary.withOpacity(0.9);

  @Deprecated('Use SubscriptionBrandingConfig.tertiary(context) instead')
  static Color accentColor(BuildContext context) => Theme.of(context).colorScheme.tertiary;
  @Deprecated('Use SubscriptionBrandingConfig.tertiary(context) instead')
  static Color accentLightColor(BuildContext context) => Theme.of(context).colorScheme.tertiary.withOpacity(0.7);
  @Deprecated('Use SubscriptionBrandingConfig.tertiary(context) instead')
  static Color accentDarkColor(BuildContext context) => Theme.of(context).colorScheme.tertiary.withOpacity(0.9);

  @Deprecated('Use SubscriptionBrandingConfig.success(context) instead')
  static Color activeSubscriptionColor(BuildContext context) => Theme.of(context).colorScheme.primary;
  @Deprecated('Use SubscriptionBrandingConfig.info(context) instead')
  static Color trialSubscriptionColor(BuildContext context) => Theme.of(context).colorScheme.tertiary;
  @Deprecated('Use SubscriptionBrandingConfig.error(context) instead')
  static Color expiredSubscriptionColor(BuildContext context) => Theme.of(context).colorScheme.error;
  @Deprecated('Use SubscriptionBrandingConfig.warning(context) instead')
  static Color pendingSubscriptionColor(BuildContext context) => Theme.of(context).colorScheme.secondary;

  @Deprecated('Use SubscriptionBrandingConfig.price(context) instead')
  static Color priceColor(BuildContext context) => Theme.of(context).colorScheme.primary;
  @Deprecated('Use SubscriptionBrandingConfig.success(context) instead')
  static Color discountColor(BuildContext context) => Theme.of(context).colorScheme.primary;
  @Deprecated('Use SubscriptionBrandingConfig.tertiary(context) instead')
  static Color savingsColor(BuildContext context) => Theme.of(context).colorScheme.tertiary;

  // === Context-aware Gradients ===

  /// Primary gradient for hero sections and main CTAs
  static LinearGradient primaryGradient(BuildContext context) {
    final primaryColor = primary(context);
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [primaryColor, ColorUtils.safeWithAlpha(primaryColor, 0.8)],
    );
  }

  /// Secondary gradient for highlights and featured plans
  static LinearGradient secondaryGradient(BuildContext context) {
    final secondaryColor = secondary(context);
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [secondaryColor, ColorUtils.safeWithAlpha(secondaryColor, 0.8)],
    );
  }

  /// Success gradient for active subscriptions
  static LinearGradient successGradient(BuildContext context) {
    final successColor = success(context);
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [successColor, ColorUtils.safeWithAlpha(successColor, 0.8)],
    );
  }

  /// Premium gradient for high-tier plans
  static LinearGradient premiumGradient(BuildContext context) {
    final tertiaryColor = tertiary(context);
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [tertiaryColor, ColorUtils.safeWithAlpha(tertiaryColor, 0.8)],
    );
  }

  // === Typography ===

  /// Heading styles for subscription screens
  static TextStyle headingLarge(BuildContext context) => TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Theme.of(context).colorScheme.onSurface,
    height: 1.2,
  );

  static TextStyle headingMedium(BuildContext context) => TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Theme.of(context).colorScheme.onSurface,
    height: 1.3,
  );

  static TextStyle headingSmall(BuildContext context) => TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: Theme.of(context).colorScheme.onSurface,
    height: 1.4,
  );

  /// Price typography
  static TextStyle priceStyle(BuildContext context) => TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: price(context),
    height: 1,
  );

  static TextStyle priceCurrency(BuildContext context) => TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: Theme.of(context).colorScheme.onSurfaceVariant,
    height: 1,
  );

  static TextStyle discountStyle(BuildContext context) => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: success(context),
  );

  /// Plan feature typography
  static TextStyle featureTitle(BuildContext context) => TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Theme.of(context).colorScheme.onSurface,
  );

  static TextStyle featureDescription(BuildContext context) => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: Theme.of(context).colorScheme.onSurfaceVariant,
    height: 1.4,
  );

  // === Context-aware Button Styles ===

  /// Primary CTA button style
  static ButtonStyle primaryButtonStyle(BuildContext context) => ElevatedButton.styleFrom(
    backgroundColor: primary(context),
    foregroundColor: Theme.of(context).colorScheme.onPrimary,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    elevation: 2,
    shadowColor: ColorUtils.safeWithAlpha(primary(context), 0.3),
  );

  /// Secondary button style
  static ButtonStyle secondaryButtonStyle(BuildContext context) => ElevatedButton.styleFrom(
    backgroundColor: secondary(context),
    foregroundColor: Theme.of(context).colorScheme.onSecondary,
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    elevation: 1,
    shadowColor: ColorUtils.safeWithAlpha(secondary(context), 0.2),
  );

  /// Outlined button style
  static ButtonStyle outlinedButtonStyle(BuildContext context) => OutlinedButton.styleFrom(
    foregroundColor: primary(context),
    side: BorderSide(color: primary(context), width: 1.5),
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
  );

  /// Text button style
  static ButtonStyle textButtonStyle(BuildContext context) => TextButton.styleFrom(
    foregroundColor: primary(context),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  );

  // (BuildContext context) => == Context-aware Card Styles ===

  /// Standard subscription card decoration
  static BoxDecoration cardDecoration(BuildContext context) => BoxDecoration(
    color: Theme.of(context).colorScheme.surface,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: Theme.of(context).colorScheme.outline),
    boxShadow: [
      BoxShadow(
        color: ColorUtils.safeWithAlpha(Theme.of(context).colorScheme.shadow, 0.05),
        spreadRadius: 1,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  );

  /// Selected plan card decoration
  static BoxDecoration selectedCardDecoration(BuildContext context) => BoxDecoration(
    color: Theme.of(context).colorScheme.surface,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: primary(context), width: 2),
    boxShadow: [
      BoxShadow(
        color: ColorUtils.safeWithAlpha(primary(context), 0.1),
        spreadRadius: 1,
        blurRadius: 12,
        offset: const Offset(0, 4),
      ),
    ],
  );

  /// Popular plan card decoration
  static BoxDecoration popularCardDecoration(BuildContext context) => BoxDecoration(
    color: Theme.of(context).colorScheme.surface,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: secondary(context), width: 2),
    boxShadow: [
      BoxShadow(
        color: ColorUtils.safeWithAlpha(secondary(context), 0.15),
        spreadRadius: 2,
        blurRadius: 16,
        offset: const Offset(0, 6),
      ),
    ],
  );

  /// Premium plan card decoration
  static BoxDecoration premiumCardDecoration(BuildContext context) => BoxDecoration(
    gradient: premiumGradient(context),
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: ColorUtils.safeWithAlpha(tertiary(context), 0.2),
        spreadRadius: 2,
        blurRadius: 20,
        offset: const Offset(0, 8),
      ),
    ],
  );

  // === Context-aware Icon Styles ===

  /// Feature icon style
  static IconThemeData featureIconTheme(BuildContext context) => IconThemeData(
    color: success(context),
    size: 20,
  );

  /// Premium feature icon style
  static IconThemeData premiumIconTheme(BuildContext context) => IconThemeData(
    color: tertiary(context),
    size: 22,
  );

  /// Status icon styles
  static IconThemeData statusIconTheme(Color color) =>
      IconThemeData(color: color, size: 18);

  // === Spacing & Sizing ===

  /// Standard padding for subscription screens
  static const EdgeInsets screenPadding = EdgeInsets.all(16);
  static const EdgeInsets cardPadding = EdgeInsets.all(20);
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(
    horizontal: 24,
    vertical: 16,
  );

  /// Standard spacing values
  static const double spacingXSmall = 4;
  static const double spacingSmall = 8;
  static const double spacingMedium = 16;
  static const double spacingLarge = 24;
  static const double spacingXLarge = 32;

  /// Border radius values
  static const double radiusSmall = 8;
  static const double radiusMedium = 12;
  static const double radiusLarge = 16;
  static const double radiusXLarge = 20;

  // === Context-aware Utility Methods ===

  /// Get subscription status color
  static Color getSubscriptionStatusColor(BuildContext context, String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return success(context);
      case 'trial':
        return info(context);
      case 'expired':
      case 'canceled':
        return error(context);
      case 'pending':
      case 'past_due':
        return warning(context);
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  /// Get tier-specific color
  static Color getTierColor(BuildContext context, String tier) {
    switch (tier.toLowerCase()) {
      case 'starter':
        return info(context);
      case 'basic':
        return primary(context);
      case 'premium':
        return secondary(context);
      case 'anchor':
        return tertiary(context);
      case 'enterprise':
        return Theme.of(context).colorScheme.onSurface;
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  /// Get tier-specific gradient
  static LinearGradient getTierGradient(BuildContext context, String tier) {
    switch (tier.toLowerCase()) {
      case 'premium':
      case 'anchor':
      case 'enterprise':
        return premiumGradient(context);
      case 'basic':
        return primaryGradient(context);
      default:
        return LinearGradient(
          colors: [
            Theme.of(context).colorScheme.surfaceContainerHighest,
            ColorUtils.safeWithAlpha(Theme.of(context).colorScheme.onSurfaceVariant, 0.1),
          ],
        );
    }
  }

  /// Create themed app bar
  static AppBar createSubscriptionAppBar(BuildContext context, {
    required String title,
    List<Widget>? actions,
    bool centerTitle = true,
  }) {
    return AppBar(
      title: Text(
        title,
        style: headingMedium(context).copyWith(color: Theme.of(context).colorScheme.onPrimary),
      ),
      centerTitle: centerTitle,
      backgroundColor: primary(context),
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
      elevation: 0,
      actions: actions,
    );
  }

  /// Create popular plan badge
  static Widget createPopularBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: secondary(context),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        'الأكثر شعبية',
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSecondary,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Create discount badge
  static Widget createDiscountBadge(BuildContext context, String discount) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: tertiary(context),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        discount,
        style: TextStyle(
          color: Theme.of(context).colorScheme.onTertiary,
          fontSize: 11,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // === Legacy static constants for backward compatibility ===
  // (These will be removed in a future version)
  @Deprecated('Use primaryGradient(context) instead')
  static const LinearGradient legacyPrimaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryColor, primaryDarkColor],
  );

  @Deprecated('Use secondaryGradient(context) instead')
  static const LinearGradient legacySecondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryColor, secondaryDarkColor],
  );

  @Deprecated('Use successGradient(context) instead')
  static LinearGradient legacySuccessGradient(BuildContext context) => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [activeSubscriptionColor(context), Theme.of(context).colorScheme.primary],
  );

  @Deprecated('Use premiumGradient(context) instead')
  static LinearGradient legacyPremiumGradient(BuildContext context) => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentColor(context), accentDarkColor(context)],
  );

  @Deprecated('Use headingLarge(context) instead')
  static TextStyle legacyHeadingLarge(BuildContext context) => TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Theme.of(context).colorScheme.onSurface,
    height: 1.2,
  );

  @Deprecated('Use headingMedium(context) instead')
  static TextStyle legacyHeadingMedium(BuildContext context) => TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Theme.of(context).colorScheme.onSurface,
    height: 1.3,
  );

  @Deprecated('Use headingSmall(context) instead')
  static TextStyle legacyHeadingSmall(BuildContext context) => TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: Theme.of(context).colorScheme.onSurface,
    height: 1.4,
  );

  @Deprecated('Use priceStyle(context) instead')
  static const TextStyle legacyPriceStyle = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: priceColor,
    height: 1,
  );

  @Deprecated('Use priceCurrency(context) instead')
  static TextStyle legacyPriceCurrency(BuildContext context) => TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: Theme.of(context).colorScheme.onSurfaceVariant,
    height: 1,
  );

  @Deprecated('Use discountStyle(context) instead')
  static TextStyle legacyDiscountStyle(BuildContext context) => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: discountColor(context),
  );

  @Deprecated('Use featureTitle(context) instead')
  static TextStyle legacyFeatureTitle(BuildContext context) => TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Theme.of(context).colorScheme.onSurface,
  );

  @Deprecated('Use featureDescription(context) instead')
  static TextStyle legacyFeatureDescription(BuildContext context) => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: Theme.of(context).colorScheme.onSurfaceVariant,
    height: 1.4,
  );

  @Deprecated('Use primaryButtonStyle(context) instead')
  static ButtonStyle legacyPrimaryButtonStyle(BuildContext context) => ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    foregroundColor: Theme.of(context).colorScheme.onPrimary,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    elevation: 2,
    shadowColor: primaryColor.withValues(alpha: 0.3),
  );

  @Deprecated('Use secondaryButtonStyle(context) instead')
  static ButtonStyle legacySecondaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: secondaryColor,
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    elevation: 1,
    shadowColor: secondaryColor.withValues(alpha: 0.2),
  );

  @Deprecated('Use outlinedButtonStyle(context) instead')
  static ButtonStyle legacyOutlinedButtonStyle = OutlinedButton.styleFrom(
    foregroundColor: primaryColor,
    side: const BorderSide(color: primaryColor, width: 1.5),
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
  );

  @Deprecated('Use textButtonStyle(context) instead')
  static ButtonStyle legacyTextButtonStyle(BuildContext context) => TextButton.styleFrom(
    foregroundColor: primaryColor(context),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  );

  @Deprecated('Use cardDecoration(context) instead')
  static BoxDecoration legacyCardDecoration(BuildContext context) => BoxDecoration(
    color: Theme.of(context).colorScheme.surface,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: Theme.of(context).colorScheme.outline),
    boxShadow: [
      BoxShadow(
        color: AppColors.black.withValues(alpha: 0.05),
        spreadRadius: 1,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  );

  @Deprecated('Use selectedCardDecoration(context) instead')
  static BoxDecoration legacySelectedCardDecoration(BuildContext context) => BoxDecoration(
    color: Theme.of(context).colorScheme.surface,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: primaryColor, width: 2),
    boxShadow: [
      BoxShadow(
        color: primaryColor(context).withOpacity(0.1),
        spreadRadius: 1,
        blurRadius: 12,
        offset: const Offset(0, 4),
      ),
    ],
  );

  @Deprecated('Use popularCardDecoration(context) instead')
  static BoxDecoration legacyPopularCardDecoration(BuildContext context) => BoxDecoration(
    color: Theme.of(context).colorScheme.surface,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: secondaryColor, width: 2),
    boxShadow: [
      BoxShadow(
        color: secondaryColor.withValues(alpha: 0.15),
        spreadRadius: 2,
        blurRadius: 16,
        offset: const Offset(0, 6),
      ),
    ],
  );

  @Deprecated('Use premiumCardDecoration(context) instead')
  static BoxDecoration legacyPremiumCardDecoration(BuildContext context) => BoxDecoration(
    gradient: legacyPremiumGradient(context),
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: accentColor(context).withOpacity(0.2),
        spreadRadius: 2,
        blurRadius: 20,
        offset: const Offset(0, 8),
      ),
    ],
  );

  @Deprecated('Use featureIconTheme(context) instead')
  static IconThemeData legacyFeatureIconTheme(BuildContext context) => IconThemeData(
    color: Theme.of(context).colorScheme.primary,
    size: 20,
  );

  @Deprecated('Use premiumIconTheme(context) instead')
  static IconThemeData legacyPremiumIconTheme(BuildContext context) => IconThemeData(
    color: accentColor(context),
    size: 22,
  );
}
