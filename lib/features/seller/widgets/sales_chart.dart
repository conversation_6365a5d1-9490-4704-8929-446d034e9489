import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../providers/sales_stats_provider.dart' as provider;

class SalesChart extends ConsumerWidget {
  const SalesChart({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chartData = ref.watch(provider.dailySalesChartDataProvider);

    return chartData.when(
      data: (data) {
        if (data.isEmpty) {
          return const SizedBox(
            height: 200,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        return Container(
          height: 300,
          padding: const EdgeInsets.all(AppTheme.spacingM),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(AppTheme.radiusM),
            boxShadow: const [
              BoxShadow(
                color: Color.fromRGBO(
                  0,
                  0,
                  0,
                  05,
                ), // Replaced withOpacity with fromRGBO
                blurRadius: 5,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'إجمالي المبيعات لآخر 30 يوم',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  Row(
                    children: [
                      Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2),
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingXS),
                      Text(
                        'المبيعات (ر.س)',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingM),
              Expanded(
                child: LineChart(
                  LineChartData(
                    gridData: FlGridData(
                      drawVerticalLine: false,
                      horizontalInterval: 5000,
                      getDrawingHorizontalLine: (value) => const FlLine(
                        color: Color.fromRGBO(158, 158, 158, 0.2),
                        strokeWidth: 1,
                      ),
                    ),
                    titlesData: FlTitlesData(
                      rightTitles: const AxisTitles(),
                      topTitles: const AxisTitles(),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 30,
                          interval: 5,
                          getTitlesWidget: (value, meta) {
                            if (value.toInt() >= data.length ||
                                value.toInt() % 5 != 0) {
                              return const SizedBox();
                            }

                            final dateString = data[value.toInt()].date;
                            return Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Text(
                                dateString.isNotEmpty
                                    ? DateFormat('d/M').format(DateTime.parse(dateString))
                                    : '-',
                                style: const TextStyle(fontSize: 10),
                              ),
                            );
                          },
                        ),
                      ),
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          interval: 5000,
                          reservedSize: 40,
                          getTitlesWidget: (value, meta) {
                            if (value == 0) {
                              return const SizedBox();
                            }
                            return Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: Text(
                                _formatCurrency(value),
                                style: const TextStyle(fontSize: 10),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    borderData: FlBorderData(show: false),
                    minX: 0,
                    maxX: data.length.toDouble() - 1,
                    minY: 0,
                    maxY: _findMaxRevenue(data) * 1.2,
                    lineBarsData: [
                      LineChartBarData(
                        spots: _createSpots(data),
                        isCurved: true,
                        color: Theme.of(context).colorScheme.primary,
                        barWidth: 3,
                        isStrokeCapRound: true,
                        dotData: const FlDotData(show: false),
                        belowBarData: BarAreaData(
                          show: true,
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withAlpha((0.1 * 255).toInt()),
                        ),
                      ),
                    ],
                    lineTouchData: LineTouchData(
                      touchTooltipData: LineTouchTooltipData(
                        tooltipBorderRadius: BorderRadius.circular(8),
                        getTooltipItems: (List<LineBarSpot> touchedBarSpots) =>
                            touchedBarSpots.map((barSpot) {
                              final flSpot = barSpot;
                              if (flSpot.x.toInt() >= data.length) {
                                return null;
                              }

                              final index = flSpot.x.toInt();
                              final dailySalesData = data[index];
                              final formattedDate = dailySalesData.date.isNotEmpty
                                  ? DateFormat(
                                      'yyyy/MM/dd',
                                    ).format(DateTime.parse(dailySalesData.date))
                                  : 'Unknown date';
                              final formattedRevenue = NumberFormat(
                                '#,##00',
                                'ar',
                              ).format(dailySalesData.revenue);

                              return LineTooltipItem(
                                '$formattedDate\n',
                                const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                                children: [
                                  TextSpan(
                                    text: '$formattedRevenue ر.س\n',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ),
                                  TextSpan(
                                    text: 'الطلبات: ${dailySalesData.sales}',
                                    style: TextStyle(
                                      color: Colors.white.withAlpha(
                                        (0.8 * 255).toInt(),
                                      ),
                                      fontWeight: FontWeight.normal,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox(
        height: 200,
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) =>
          Center(child: Text('Error loading sales data: $error')),
    );
  }

  List<FlSpot> _createSpots(List<provider.DailySales> data) => List.generate(
    data.length,
    (index) => FlSpot(index.toDouble(), data[index].revenue),
  );

  double _findMaxRevenue(List<provider.DailySales> data) {
    double maxRevenue = 0;
    for (final day in data) {
      if (day.revenue > maxRevenue) {
        maxRevenue = day.revenue;
      }
    }
    return maxRevenue;
  }

  String _formatCurrency(double value) {
    if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    }
    return value.toStringAsFixed(0);
  }
}
