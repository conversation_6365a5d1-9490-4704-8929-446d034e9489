import 'package:flutter/material.dart';

/// البطاقة التي تعرض ملف البائع الشخصي
class SellerProfileCard extends StatelessWidget {
  const SellerProfileCard({
    required this.name,
    super.key,
    this.logoUrl,
    this.description,
  });

  final String name;
  final String? logoUrl;
  final String? description;

  @override
  Widget build(BuildContext context) {
    return Card(
    margin: const EdgeInsets.all(8),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (logoUrl != null)
                CircleAvatar(
                  backgroundImage: NetworkImage(logoUrl!),
                  radius: 24,
                )
              else
                const CircleAvatar(radius: 24, child: Icon(Icons.store)),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  name,
                  style: Theme.of(context).textTheme.titleMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          if (description != null) ...[
            const SizedBox(height: 12),
            Text(
              description!,
              style: Theme.of(context).textTheme.bodyMedium,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    ),
    );
  }
}
