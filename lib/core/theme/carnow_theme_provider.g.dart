// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'carnow_theme_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$themePreferencesNotifierHash() =>
    r'4b6f680abb646746d20cb729a2a8553853dada85';

/// Theme preferences notifier with persistence
///
/// Copied from [ThemePreferencesNotifier].
@ProviderFor(ThemePreferencesNotifier)
final themePreferencesNotifierProvider =
    AutoDisposeNotifierProvider<
      ThemePreferencesNotifier,
      ThemePreferences
    >.internal(
      ThemePreferencesNotifier.new,
      name: r'themePreferencesNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$themePreferencesNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ThemePreferencesNotifier = AutoDisposeNotifier<ThemePreferences>;
String _$carNowThemeNotifierHash() =>
    r'013ae3510093156e5e97ab6b24273cbc6ce793dd';

/// Main theme provider that generates Material 3 themes
///
/// Copied from [CarNowThemeNotifier].
@ProviderFor(CarNowThemeNotifier)
final carNowThemeNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CarNowThemeNotifier, CarNowTheme>.internal(
      CarNowThemeNotifier.new,
      name: r'carNowThemeNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$carNowThemeNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CarNowThemeNotifier = AutoDisposeAsyncNotifier<CarNowTheme>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
